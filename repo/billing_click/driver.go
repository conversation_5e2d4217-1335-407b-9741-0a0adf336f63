package billing_click

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util"

	null "github.com/guregu/null/v6"
)

func (r *BillingClick) CreateDriver(ctx context.Context, driverId, parkId, rate int, phone, firstName, lastName, startDate string) (resp model.Response, err error) {
	const body = "driver_reg</action><driver_id>%d</driver_id><company_id>%d</company_id><phone>%s</phone><first_name>%s</first_name><last_name>%s</last_name><contract_date>%s</contract_date><rate>%d</rate></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, driverId, parkId, phone, firstName, lastName, startDate, rate)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) UpdateDriver(ctx context.Context, driverId, parkId, rate int, phone, firstName, lastName, startDate string) (resp model.Response, err error) {
	const body = "driver_edit</action><driver_id>%d</driver_id><company_id>%d</company_id><phone>%s</phone><first_name>%s</first_name><last_name>%s</last_name><contract_date>%s</contract_date><rate>%d</rate></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, driverId, parkId, phone, firstName, lastName, startDate, rate)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) ActivateDriver(ctx context.Context, driverId int) (resp model.Response, err error) {
	req := r.bodyHeader + fmt.Sprintf("driver_active</action><driver_id>%d</driver_id></ROOT>", driverId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) DeactivateDriver(ctx context.Context, driverId int) (resp model.Response, err error) {
	req := r.bodyHeader + fmt.Sprintf("driver_fire</action><driver_id>%d</driver_id></ROOT>", driverId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) GetDriverBalance(ctx context.Context, driverId int) (resp model.DriverBalance, err error) {
	req := r.bodyHeader + fmt.Sprintf("get_acc_bal_driver</action><driver_id>%d</driver_id></ROOT>", driverId)
	var result struct {
		ROOT
		Balance      float64     `xml:"bal"`
		LastFillDate null.String `xml:"last_fill_date"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		return resp, errors.New(result.ErrorNote)
	}

	resp.Balance = int(result.Balance)
	resp.LastFillDate = result.LastFillDate.String

	return
}

type balanceHistoryItem struct {
	Time          int                       `xml:"datetime"`
	AmountType    string                    `xml:"sign"`
	Amount        string                    `xml:"amount"`
	Details       balanceHistoryItemDetails `xml:"data"`
	TypeOperation string                    `xml:"type_operation,omitempty"`
}

type balanceHistoryItemDetails struct {
	PayType string `xml:"rate"`
	Type    string `xml:"type_pay"`
	OrderId string `xml:"ride_id"`
	Comment string `xml:"comment"`
}

func (r *BillingClick) GetDriverBalanceHistory(ctx context.Context, driverId, limit, skip int) (resp []model.DriverBalanceHistory, err error) {
	req := r.bodyHeader + fmt.Sprintf("history_driver</action><driver>%d</driver><limit>%d</limit><skip>%d</skip><operation>transfer_promo_main</operation></ROOT>", driverId, limit, skip)

	var result struct {
		ROOT
		Items []balanceHistoryItem `xml:"item"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 || result.SuccessCode != 1 {
		err = errors.New(result.ErrorNote)
		return
	}

	resp = make([]model.DriverBalanceHistory, len(result.Items))

	for i, r := range result.Items {
		if r.TypeOperation == "transfer_promo_main" {
			r.Details.Type = "Бонус"
		}

		if r.Details.OrderId == "" {
			r.Details.OrderId = "-"
		}

		if r.Details.PayType == "" {
			r.Details.PayType = r.Details.Type
		}

		if r.AmountType == "-" && r.Details.OrderId != "" && r.Details.PayType != "" {
			r.Details.Comment = "Комиссия за заказ " + r.Details.OrderId
			r.Details.PayType = r.Details.PayType + "%"
		} else if (r.Details.PayType == "Paynet" && r.Details.Comment == "") || r.Details.Comment == "Пополнение счета с Payme" {
			r.Details.Comment = "Пополнение баланса"
		}

		var orderId string
		if r.Details.OrderId != "" {
			orderId = r.Details.OrderId
		}

		// Handle client_insure operation type with negative amount
		amount := util.TrimStringFromDot(r.Amount)
		if r.TypeOperation == "client_insure" {
			// Convert amount to negative if it's not already negative
			if amount != "" && amount[0] != '-' {
				amount = "-" + amount
			}
		}

		resp[i] = model.DriverBalanceHistory{
			Time:       r.Time,
			AmountType: r.AmountType,
			Amount:     amount,
			PayType:    r.Details.PayType,
			OrderId:    orderId,
			Comment:    r.Details.Comment,
		}
	}

	return
}

func (r *BillingClick) RefillDriverBalance(ctx context.Context, driverId, amount, amountType, accountId int, comment string) (resp int, err error) {
	const body = "driver_deposit</action><mytaxi_id>%d</mytaxi_id><driver_id>%d</driver_id><amount>%d</amount><cash>%d</cash><id_account>%d</id_account><without_taxipark>1</without_taxipark><description>%s</description>"
	requset := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), driverId, amount, amountType, accountId, comment)
	var result ROOT

	if amountType == 7 {
		requset += fmt.Sprintf("<service>4</service><dtime>%s</dtime></ROOT>", time.Now().Format("2006-01-02 15:04:05"))
	} else {
		requset += "</ROOT>"
	}

	err = r.sendRequest(ctx, []byte(requset), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		if result.ErrorNote == "Транзакция уже существует" {
			return r.RefillDriverBalance(ctx, driverId, amount, amountType, accountId, comment) // TODO recursive for transaction id
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	resp = int(result.Balance)

	return
}

func (r *BillingClick) RefineDriverBalance(ctx context.Context, driverId, amount, accountId int, comment string) (resp int, err error) {
	const body = "driver_fine</action><mytaxi_id>%d</mytaxi_id><driver_id>%d</driver_id><amount>%d</amount><id_account_to>%d</id_account_to><comment>%s</comment></ROOT>"
	request := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), driverId, amount, accountId, comment)

	var result ROOT

	err = r.sendRequest(ctx, []byte(request), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		if result.ErrorNote == "Транзакция уже существует" {
			return r.RefineDriverBalance(ctx, driverId, amount, accountId, comment) // TODO recursive for transaction id
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	resp = int(result.Balance)

	return
}

func (r *BillingClick) DriverTransferToDriver(ctx context.Context, senderID, receiverID, amount int) (err error) {
	body := fmt.Sprintf("driver_to_driver</action><mytaxi_id>%d</mytaxi_id><sender_id>%d</sender_id><receiver_id>%d</receiver_id><amount>%d</amount></ROOT>", time.Now().UnixMilli(), senderID, receiverID, amount)
	req := r.bodyHeader + body
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		if result.ErrorNote == "Транзакция уже существует" {
			return r.DriverTransferToDriver(ctx, senderID, receiverID, amount) // TODO recursive for transaction id
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}

func (r *BillingClick) GetDriverBalanceHistoryV2(ctx context.Context, driverId, limit, skip int) (resp model.DriverBalanceHistoryResponseV2, err error) {
	req := r.bodyHeader + fmt.Sprintf("get_driver_balance_history</action><driver_id>%d</driver_id><limit>%d</limit><skip>%d</skip></ROOT>", driverId, limit, skip)

	type balanceHistoryItemV2 struct {
		Created     string `xml:"created"`
		Balance     string `xml:"balance"`
		Dt          string `xml:"dt"`
		Ct          string `xml:"ct"`
		OperType    string `xml:"oper_type"`
		ServiceType string `xml:"service_type"`
	}

	var result struct {
		ROOT
		Items []balanceHistoryItemV2 `xml:"item"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		err = errors.New(result.ErrorNote)
		return
	}

	resp.Deposit = make([]model.DriverBalanceHistoryV2, len(result.Items))

	for i, item := range result.Items {
		var amount string
		var amountType string
		if item.Dt != "" {
			amount = util.TrimStringFromDot(item.Dt)
			amountType = "+"
		} else if item.Ct != "" {
			amount = util.TrimStringFromDot(item.Ct)
			amountType = "-"
		}

		resp.Deposit[i] = model.DriverBalanceHistoryV2{
			Time:       item.Created,
			AmountType: amountType,
			Amount:     amount,
			Balance:    util.TrimStringFromDot(item.Balance),
			Comment:    item.OperType,
		}
	}

	return
}
